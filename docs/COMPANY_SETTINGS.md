# Cấu hình thông tin công ty

## Tổng quan
Dự án sử dụng gem `config` để quản lý thông tin liên hệ của công ty một cách tập trung và dễ dàng.

## Thông tin công ty hiện tại

### Cấu hình trong `config/settings.yml`:
```yaml
company:
  name: "CÔNG TY TNHH SEA EDUCATION TRAINING"
  email: "<EMAIL>"
  phone: "+84 *********"
  address: "26 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tp Hà Nội"
  tax_code: "0111012089"
```

## Cách sử dụng trong code

### 1. Truy cập thông tin công ty:
```ruby
# Tên công ty
Settings.company.name

# Email liên hệ
Settings.company.email

# Số điện thoại
Settings.company.phone

# Địa chỉ
Settings.company.address

# Mã số thuế
Settings.company.tax_code
```

### 2. Sử dụng trong views:
```erb
<!-- Email liên hệ -->
<%= mail_to Settings.company.email, Settings.company.email %>

<!-- Số điện thoại -->
<%= link_to Settings.company.phone, "tel:#{Settings.company.phone}" %>

<!-- Địa chỉ -->
<p><%= Settings.company.address %></p>

<!-- Thông tin đầy đủ -->
<div class="company-info">
  <h3><%= Settings.company.name %></h3>
  <p>Email: <%= Settings.company.email %></p>
  <p>Điện thoại: <%= Settings.company.phone %></p>
  <p>Địa chỉ: <%= Settings.company.address %></p>
  <p>MST: <%= Settings.company.tax_code %></p>
</div>
```

### 3. Sử dụng trong mailers:
```ruby
class ApplicationMailer < ActionMailer::Base
  default from: Settings.company.email
  layout 'mailer'
end

# Hoặc trong email template
<%= Settings.company.name %>
<%= Settings.company.email %>
<%= Settings.company.phone %>
```

### 4. Sử dụng trong controllers:
```ruby
class ContactController < ApplicationController
  def index
    @company_info = {
      name: Settings.company.name,
      email: Settings.company.email,
      phone: Settings.company.phone,
      address: Settings.company.address
    }
  end
end
```

## Kiểm tra cấu hình

### Rails Console:
```bash
rails console

# Xem tất cả thông tin công ty
Settings.company

# Xem thông tin cụ thể
Settings.company.email
Settings.company.phone
```

### Kết quả mong đợi:
```ruby
Settings.company.email
# => "<EMAIL>"

Settings.company.phone
# => "+84 *********"
```

## Ưu điểm của cách tiếp cận này

1. **Tập trung**: Tất cả thông tin công ty ở một nơi
2. **Dễ bảo trì**: Thay đổi một lần, áp dụng toàn bộ ứng dụng
3. **Type-safe**: Gem config cung cấp interface rõ ràng
4. **Environment-aware**: Có thể override theo environment nếu cần
5. **Consistent**: Đảm bảo thông tin nhất quán trên toàn hệ thống

## Lưu ý

- Thông tin công ty được cấu hình trong `config/settings.yml` (chung cho tất cả environment)
- Không cần override trong các file environment riêng vì thông tin này không thay đổi theo môi trường
- Luôn sử dụng `Settings.company.*` thay vì hardcode thông tin trong code
- Khi cần thay đổi thông tin, chỉ cần sửa trong file `config/settings.yml`

## Các file đã được cập nhật

Các file sau đã được cập nhật để sử dụng `Settings.company.*` thay vì hardcode:

### Mailers:
- `app/mailers/application_mailer.rb` - default from email
- `app/mailers/user_mailer.rb` - from email
- `app/mailers/contact_mailer.rb` - admin email
- `config/initializers/devise.rb` - mailer_sender

### Views:
- `app/views/shared/_footer.html.erb` - thông tin công ty trong footer
- `app/views/user_mailer/registration_confirmation.text.erb` - email template
- `app/views/about/_contact.html.erb` - trang liên hệ
- `app/views/exam_registration_forms/_form.html.erb` - form đăng ký
- `app/views/home/<USER>/_content.html.erb` - trang lịch thi
- `app/views/contacts/index.html.erb` - trang liên hệ meta description
- `app/views/home/<USER>

### Controllers:
- `app/controllers/contacts_controller.rb` - meta description và error messages

### Helpers:
- `app/helpers/seo/structured_data_helper.rb` - SEO structured data

### Services:
- `app/services/sepay_service.rb` - sử dụng Settings.company.name cho account_name
- `app/services/qr_payment_service.rb` - sử dụng Settings.company.name cho account_name

### Tests:
- `test/services/sepay_service_test.rb` - cập nhật test để sử dụng Settings.company.name

## Ví dụ thực tế

### Footer website:
```erb
<footer class="footer">
  <div class="container">
    <div class="row">
      <div class="col-md-6">
        <h5><%= Settings.company.name %></h5>
        <p><%= Settings.company.address %></p>
      </div>
      <div class="col-md-6">
        <p>Email: <%= mail_to Settings.company.email %></p>
        <p>Điện thoại: <%= link_to Settings.company.phone, "tel:#{Settings.company.phone}" %></p>
        <p>MST: <%= Settings.company.tax_code %></p>
      </div>
    </div>
  </div>
</footer>
```

### Email signature:
```erb
<div class="email-signature">
  <p><strong><%= Settings.company.name %></strong></p>
  <p><%= Settings.company.address %></p>
  <p>Email: <%= Settings.company.email %> | Điện thoại: <%= Settings.company.phone %></p>
</div>
```
