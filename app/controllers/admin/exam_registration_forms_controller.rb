# frozen_string_literal: true

module Admin
  class ExamRegistrationFormsController < Admin::ApplicationController
    before_action :set_exam_registration_form, only: %i[show mark_as_paid]

    def index
      # Tạo options cho exam center addresses
      @exam_center_addresses = Master::ExamCenter.all.pluck(:address).map { |addr| [addr, addr] }
      # Tạo options cho payment status với text đã translate nhưng value là enum key
      @payment_statuses = ExamRegistrationForm.payment_statuses.map do |key, _value|
        [I18n.t("payment_status.#{key}", default: key.humanize), key]
      end
      @q = ExamRegistrationForm.ransack(params[:q])
      @exam_registration_forms = @q.result.order(created_at: :desc).page(params[:page]).per(20)
      @selected_exam_center_name = params.dig(:q, :exam_center_name_cont)
      @selected_payment_status = params.dig(:q, :payment_status_eq)
    end

    def show; end

    def mark_as_paid
      respond_to do |format|
        if @exam_registration_form.payment_pending?
          begin
            @exam_registration_form.mark_as_paid_by_admin!
            format.html { redirect_to admin_exam_registration_forms_path, notice: t('.success') }
            format.json { render json: { status: 'success', message: 'Đã đánh dấu thanh toán thành công!' } }
          rescue StandardError => e
            Rails.logger.error "Error marking as paid: #{e.message}"
            format.html { redirect_to admin_exam_registration_forms_path, alert: t('.error') }
            format.json do
              render json: { status: 'error', message: 'Có lỗi xảy ra khi cập nhật!' }, status: :unprocessable_entity
            end
          end
        else
          format.html do
            redirect_to admin_exam_registration_forms_path, alert: t('.invalid')
          end
          format.json do
            render json: { status: 'error', message: 'Không thể đánh dấu thanh toán cho đơn này!' },
                   status: :unprocessable_entity
          end
        end
      end
    end

    def export_excel
      @q = ExamRegistrationForm.ransack(params[:q])
      @exam_registration_data = @q.result.order(created_at: :desc)
      respond_to do |format|
        format.xlsx do
          file_name = "Đơn-đăng-ký-dự-thi-#{Time.now.strftime('%Y%m%d%H%M%S')}.xlsx"

          response.headers['Content-Disposition'] = "attachment; filename=#{file_name}"
        end
      end
    end

    private

    def set_exam_registration_form
      @exam_registration_form = ExamRegistrationForm.find(params[:id])
    end
  end
end
