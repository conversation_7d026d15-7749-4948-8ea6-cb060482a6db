<%
content_for :title, "<PERSON>ên hệ - Góp ý"
content_for :description, "<PERSON>ên hệ với SEA Education để được tư vấn về chứng chỉ NOCN. Địa chỉ: #{Settings.company.address}. Hotline: #{Settings.company.phone}. Email: #{Settings.company.email}"
%>

<div class="workshome">
  <div class="container">
    <div class="row">
      <div class="col-md-12">
        <%= render 'about/contact' %>
      </div>
    </div>
  </div>
</div>

<style>
.contact-sidebar {
  padding-left: 30px;
}

.contact-sidebar h4 {
  color: #2c5282;
  margin-bottom: 15px;
  font-size: 18px;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 8px;
}

.contact-hours ul {
  list-style: none;
  padding: 0;
}

.contact-hours li {
  padding: 8px 0;
  border-bottom: 1px solid #f7fafc;
}

.contact-hours li:last-child {
  border-bottom: none;
}

.map-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.social-link {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  text-decoration: none;
  border-radius: 6px;
  transition: all 0.3s ease;
  color: #4a5568;
}

.social-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  text-decoration: none;
  color: white;
}

.social-link.facebook {
  background-color: #f7fafc;
  border: 1px solid #3b82f6;
}

.social-link.facebook:hover {
  background-color: #3b82f6;
}

.social-link.youtube {
  background-color: #f7fafc;
  border: 1px solid #ef4444;
}

.social-link.youtube:hover {
  background-color: #ef4444;
}

.social-link.linkedin {
  background-color: #f7fafc;
  border: 1px solid #0077b5;
}

.social-link.linkedin:hover {
  background-color: #0077b5;
}

.social-link i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

@media (max-width: 768px) {
  .contact-sidebar {
    padding-left: 0;
    margin-top: 40px;
  }
  
  .social-links {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .social-link {
    flex: 1;
    justify-content: center;
    text-align: center;
  }
}
</style>