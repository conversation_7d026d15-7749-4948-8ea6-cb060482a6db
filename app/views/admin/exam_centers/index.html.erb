<div class="container-wrapper">
  <div class="container-lg">
    <div class="row">
      <div class="col-md-12">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title mb-0">
              <i class="fas fa-building me-2"></i>Quản lý Trung tâm thi
            </h4>
            <%= link_to admin_master_data_path, class: "btn btn-secondary" do %>
              <i class="fas fa-arrow-left me-1"></i>Quay lại Dashboard
            <% end %>
          </div>
          <div class="card-body">
            <div class="alert alert-info mb-4">
              <i class="fas fa-info-circle me-2"></i>
              <strong>Hướng dẫn:</strong> Kéo thả để sắp xếp thứ tự • Bật/tắt hiển thị • Nhấn nút "+" để thêm mới
            </div>

            <!-- Form thêm mới -->
            <div class="add-form-container mb-4" style="display: none;">
              <div class="card border-success">
                <div class="card-header bg-light">
                  <h6 class="mb-0 text-success">
                    <i class="fas fa-plus-circle me-1"></i>Thêm trung tâm thi mới
                  </h6>
                </div>
                <div class="card-body">
                  <%= form_with url: admin_exam_centers_path, method: :post, id: 'add-form', local: false do |form| %>
                    <div class="row">
                      <div class="col-md-6">
                        <%= form.text_field "exam_center[address]", class: 'form-control', placeholder: 'Nhập địa chỉ trung tâm thi...', required: true %>
                      </div>
                      <div class="col-md-2">
                        <%= form.number_field "exam_center[exam_fee]", class: 'form-control', placeholder: 'Phí thi', value: 1600000, step: 1000, min: 0, required: true %>
                        <small class="text-muted">VND</small>
                      </div>
                      <div class="col-md-2">
                        <div class="form-check form-switch">
                          <%= form.check_box "exam_center[is_visible]", { checked: true, class: 'form-check-input' }, true, false %>
                          <label class="form-check-label">Hiển thị</label>
                        </div>
                      </div>
                      <div class="col-md-2">
                        <button type="submit" class="btn btn-success w-100">
                          <i class="fas fa-save me-1"></i>Lưu
                        </button>
                      </div>
                    </div>
                  <% end %>
                  <div class="text-end mt-2">
                    <button type="button" class="btn btn-outline-secondary btn-sm cancel-add">
                      <i class="fas fa-times me-1"></i>Hủy
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Danh sách trung tâm thi -->
            <div id="exam-centers-list">
              <% if @exam_centers.any? %>
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th width="50px"><i class="fas fa-arrows-alt text-muted"></i></th>
                        <th>Địa chỉ trung tâm thi</th>
                        <th width="150px" class="text-center">Phí thi (VND)</th>
                        <th width="120px" class="text-center">Hiển thị</th>
                        <th width="100px" class="text-center">Thao tác</th>
                      </tr>
                    </thead>
                    <tbody id="sortable-list">
                      <% @exam_centers.each do |center| %>
                        <tr class="exam-center-row" data-id="<%= center.id %>">
                          <td class="text-center">
                            <i class="fas fa-grip-vertical text-muted sortable-handle" style="cursor: move;"></i>
                          </td>
                          <td>
                            <div class="exam-center-address" data-id="<%= center.id %>">
                              <span class="address-display"><%= center.address %></span>
                              <input type="text" class="form-control address-edit d-none" value="<%= center.address %>">
                            </div>
                          </td>
                          <td class="text-center">
                            <div class="exam-center-fee" data-id="<%= center.id %>">
                              <span class="fee-display"><%= format_vnd_currency(center.exam_fee) %></span>
                              <input type="number" class="form-control fee-edit d-none" value="<%= center.exam_fee.to_i %>" step="1000" min="0">
                            </div>
                          </td>
                          <td class="text-center">
                            <div class="form-check form-switch d-flex justify-content-center">
                              <%= check_box_tag "visibility_#{center.id}", "1", center.is_visible,
                                  class: "form-check-input visibility-toggle",
                                  data: { center_id: center.id } %>
                            </div>
                          </td>
                          <td class="text-center">
                            <div class="btn-group btn-group-sm">
                              <button type="button" class="btn btn-outline-primary edit-btn" data-id="<%= center.id %>">
                                <i class="fas fa-edit"></i>
                              </button>
                              <button type="button" class="btn btn-outline-danger delete-btn" data-id="<%= center.id %>">
                                <i class="fas fa-trash"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      <% end %>
                    </tbody>
                  </table>
                </div>
              <% else %>
                <div class="text-center py-5 text-muted">
                  <i class="fas fa-building fa-3x mb-3"></i>
                  <h5>Chưa có trung tâm thi nào</h5>
                  <p>Nhấn nút "Thêm trung tâm thi" để bắt đầu</p>
                </div>
              <% end %>
            </div>

            <!-- Nút thêm -->
            <div class="mt-4">
              <button type="button" class="btn btn-success btn-lg" id="add-center-btn">
                <i class="fas fa-plus me-2"></i>Thêm trung tâm thi
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Toast notifications -->
<div class="position-fixed top-0 end-0 p-3" style="z-index: 1050">
  <div id="notification-toast" class="toast" role="alert">
    <div class="toast-header">
      <strong class="me-auto">Thông báo</strong>
      <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
    </div>
    <div class="toast-body"></div>
  </div>
</div>

<style>
  .container-wrapper {
    background-color: #F8F8F8;
    padding: 20px 0;
    min-height: 100vh;
  }

  .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }

  .sortable-handle:hover {
    color: #007bff !important;
    cursor: grab;
  }

  .sortable-handle:active {
    cursor: grabbing;
  }

  .sortable-ghost {
    opacity: 0.4;
    background-color: #f8f9fa !important;
  }

  .sortable-chosen {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: scale(1.02);
  }

  .sortable-drag {
    background-color: #ffffff;
    box-shadow: 0 8px 25px rgba(0,0,0,0.25);
    border: 1px solid #007bff;
  }

  .table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
  }

  .form-switch .form-check-input {
    width: 2em;
    height: 1em;
  }

  .address-edit {
    width: 100%;
  }
</style>

<!-- Load SortableJS -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Initialize SortableJS
  const sortableElement = document.getElementById('sortable-list');
  if (sortableElement && typeof Sortable !== 'undefined') {
    const sortable = Sortable.create(sortableElement, {
      handle: '.sortable-handle',
      animation: 150,
      ghostClass: 'sortable-ghost',
      chosenClass: 'sortable-chosen',
      dragClass: 'sortable-drag',
      onEnd: function(evt) {
        // Get new order
        const positions = [];
        const rows = sortableElement.querySelectorAll('tr');
        rows.forEach(function(row) {
          const id = row.getAttribute('data-id');
          if (id) positions.push(id);
        });

        // Update positions on server
        updatePositions(positions);
      }
    });

    console.log('SortableJS initialized successfully');
  } else {
    console.error('SortableJS not loaded or sortable element not found');
  }

  // Helper function to serialize form data
  function serializeForm(form) {
    const formData = new FormData(form);
    const params = new URLSearchParams();
    for (const [key, value] of formData) {
      params.append(key, value);
    }
    return params.toString();
  }

  // Helper function to make AJAX requests
  function makeRequest(url, options = {}) {
    const defaultOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
      }
    };

    return fetch(url, { ...defaultOptions, ...options })
      .then(response => response.json());
  }

  // Show add form
  const addBtn = document.getElementById('add-center-btn');
  if (addBtn) {
    addBtn.addEventListener('click', function() {
      const container = document.querySelector('.add-form-container');
      if (container) {
        container.style.display = 'block';
        this.style.display = 'none';
        const input = container.querySelector('input[name="exam_center[address]"]');
        if (input) input.focus();
      }
    });
  }

  // Cancel add form
  const cancelBtn = document.querySelector('.cancel-add');
  if (cancelBtn) {
    cancelBtn.addEventListener('click', function() {
      const container = document.querySelector('.add-form-container');
      const addBtn = document.getElementById('add-center-btn');
      const form = document.getElementById('add-form');

      if (container) container.style.display = 'none';
      if (addBtn) addBtn.style.display = 'block';
      if (form) form.reset();
    });
  }

  // Handle add form submission
  const addForm = document.getElementById('add-form');
  if (addForm) {
    addForm.addEventListener('submit', function(e) {
      e.preventDefault();

      makeRequest(this.action, {
        method: 'POST',
        body: serializeForm(this)
      })
      .then(response => {
        if (response.status === 'success') {
          showNotification(response.message, 'success');
          location.reload();
        } else {
          showNotification(response.message, 'error');
        }
      })
      .catch(() => {
        showNotification('Có lỗi xảy ra khi thêm trung tâm thi', 'error');
      });
    });
  }

  // Handle edit button clicks
  document.addEventListener('click', function(e) {
    if (e.target.closest('.edit-btn')) {
      const btn = e.target.closest('.edit-btn');
      const centerId = btn.getAttribute('data-id');
      const addressRow = document.querySelector(`.exam-center-address[data-id="${centerId}"]`);
      const feeRow = document.querySelector(`.exam-center-fee[data-id="${centerId}"]`);

      if (addressRow && feeRow) {
        const addressDisplay = addressRow.querySelector('.address-display');
        const addressEdit = addressRow.querySelector('.address-edit');
        const feeDisplay = feeRow.querySelector('.fee-display');
        const feeEdit = feeRow.querySelector('.fee-edit');

        if (addressEdit.classList.contains('d-none')) {
          // Enter edit mode
          addressDisplay.classList.add('d-none');
          addressEdit.classList.remove('d-none');
          feeDisplay.classList.add('d-none');
          feeEdit.classList.remove('d-none');
          addressEdit.focus();
          btn.innerHTML = '<i class="fas fa-save"></i>';
          btn.classList.remove('btn-outline-primary');
          btn.classList.add('btn-success');
        } else {
          // Save changes
          const newAddress = addressEdit.value.trim();
          const newFee = parseFloat(feeEdit.value);
          if (newAddress && newFee > 0) {
            updateExamCenter(centerId, { address: newAddress, exam_fee: newFee });
            addressDisplay.textContent = newAddress;
            feeDisplay.textContent = new Intl.NumberFormat('vi-VN').format(newFee);
            addressDisplay.classList.remove('d-none');
            addressEdit.classList.add('d-none');
            feeDisplay.classList.remove('d-none');
            feeEdit.classList.add('d-none');
            btn.innerHTML = '<i class="fas fa-edit"></i>';
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-primary');
          }
        }
      }
    }
  });

  // Handle delete button clicks
  document.addEventListener('click', function(e) {
    if (e.target.closest('.delete-btn')) {
      const btn = e.target.closest('.delete-btn');
      const centerId = btn.getAttribute('data-id');
      const row = btn.closest('tr');
      const address = row.querySelector('.address-display').textContent;

      if (confirm(`Bạn có chắc chắn muốn xóa trung tâm thi "${address}"?`)) {
        makeRequest(`/admin/exam_centers/${centerId}`, {
          method: 'DELETE'
        })
        .then(response => {
          if (response.status === 'success') {
            showNotification(response.message, 'success');
            row.style.transition = 'opacity 0.3s';
            row.style.opacity = '0';
            setTimeout(() => {
              row.remove();
              checkEmptyState();
            }, 300);
          } else {
            showNotification(response.message, 'error');
          }
        })
        .catch(() => {
          showNotification('Có lỗi xảy ra khi xóa trung tâm thi', 'error');
        });
      }
    }
  });

  // Handle visibility toggle
  document.addEventListener('change', function(e) {
    if (e.target.classList.contains('visibility-toggle')) {
      const centerId = e.target.getAttribute('data-center-id');
      const isVisible = e.target.checked;

      updateExamCenter(centerId, { is_visible: isVisible });
    }
  });

  function updateExamCenter(id, data) {
    const params = new URLSearchParams();
    Object.keys(data).forEach(key => {
      params.append(`exam_center[${key}]`, data[key]);
    });

    makeRequest(`/admin/exam_centers/${id}`, {
      method: 'PATCH',
      body: params.toString()
    })
    .then(response => {
      if (response.status === 'success') {
        showNotification(response.message, 'success');
      } else {
        showNotification(response.message, 'error');
      }
    })
    .catch(() => {
      showNotification('Có lỗi xảy ra khi cập nhật', 'error');
    });
  }

  function updatePositions(positions) {
    const params = new URLSearchParams();
    positions.forEach(id => {
      params.append('positions[]', id);
    });

    makeRequest('/admin/exam_centers/update_positions', {
      method: 'PATCH',
      body: params.toString()
    })
    .then(response => {
      if (response.status === 'success') {
        showNotification(response.message, 'success');
      }
    })
    .catch(() => {
      showNotification('Có lỗi xảy ra khi cập nhật thứ tự', 'error');
    });
  }

  function showNotification(message, type) {
    const toast = document.getElementById('notification-toast');
    const body = toast.querySelector('.toast-body');

    body.textContent = message;
    toast.classList.remove('text-bg-success', 'text-bg-danger');
    toast.classList.add(type === 'success' ? 'text-bg-success' : 'text-bg-danger');

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
  }

  function checkEmptyState() {
    const sortableList = document.getElementById('sortable-list');
    const examCentersList = document.getElementById('exam-centers-list');

    if (sortableList && sortableList.querySelectorAll('tr').length === 0) {
      if (examCentersList) {
        examCentersList.innerHTML = `
          <div class="text-center py-5 text-muted">
            <i class="fas fa-building fa-3x mb-3"></i>
            <h5>Chưa có trung tâm thi nào</h5>
            <p>Nhấn nút "Thêm trung tâm thi" để bắt đầu</p>
          </div>
        `;
      }
    }
  }
});
</script>
