module Seo
  module StructuredData<PERSON>elper
    def structured_data_organization
      data = {
        "@context": "https://schema.org",
        "@type": "Organization",
        name: "SEA Education",
        alternateName: "Công ty TNHH SEA Education",
        url: "https://sea-edu.com.vn",
        logo: "https://sea-edu.com.vn/logo_sea_education.jpg",
        contactPoint: {
          "@type": "ContactPoint",
          telephone: Settings.company.phone.gsub(/\s/, '-'),
          contactType: "customer service",
          email: Settings.company.email
        },
        address: {
          "@type": "PostalAddress",
          streetAddress: Settings.company.address,
          addressLocality: "Quận Cầu <PERSON>ấ<PERSON>",
          addressRegion: "Hà Nội",
          addressCountry: "VN"
        },
        sameAs: [
          "https://sea-edu.com.vn"
        ]
      }
      safe_join([data.to_json])
    end

    def structured_data_course
      data = {
        "@context": "https://schema.org",
        "@type": "Course",
        name: "<PERSON><PERSON><PERSON> chỉ tiếng <PERSON>h NOCN",
        description: '<PERSON><PERSON><PERSON><PERSON> thi chứng chỉ tiếng Anh NOCN được công nhận quốc tế, ' \
                     'đánh giá 4 kỹ năng Nghe-Nói-Đọc-Viết theo chuẩn CEFR',
        provider: {
          "@type": "Organization",
          name: "SEA Education",
          sameAs: "https://sea-edu.com.vn"
        },
        courseCode: "NOCN",
        educationalCredentialAwarded: "NOCN Certificate",
        teaches: "English Language Proficiency"
      }
      safe_join([data.to_json])
    end

    def structured_data_faq(questions_and_answers)
      data = {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        mainEntity: questions_and_answers.map do |qa|
          {
            "@type": "Question",
            name: qa[:question],
            acceptedAnswer: {
              "@type": "Answer",
              text: qa[:answer]
            }
          }
        end
      }
      safe_join([data.to_json])
    end

    def structured_data_local_business
      data = {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        name: "SEA Education",
        image: "https://sea-edu.com.vn/logo_sea_education.jpg",
        telephone: Settings.company.phone.gsub(/\s/, '-'),
        email: Settings.company.email,
        address: {
          "@type": "PostalAddress",
          streetAddress: Settings.company.address,
          addressLocality: "Quận Cầu Giấy",
          addressRegion: "Hà Nội",
          postalCode: "100000",
          addressCountry: "VN"
        },
        geo: {
          "@type": "GeoCoordinates",
          latitude: "21.0285",
          longitude: "105.8542"
        },
        url: "https://sea-edu.com.vn",
        priceRange: "$$",
        openingHours: "Mo-Fr 08:00-17:00",
        sameAs: [
          "https://sea-edu.com.vn"
        ]
      }
      safe_join([data.to_json])
    end
  end
end
