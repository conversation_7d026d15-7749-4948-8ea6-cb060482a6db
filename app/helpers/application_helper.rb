module ApplicationHelper
  def link_state(path)
    request_path = URI.parse(request.fullpath).path
    target_path = URI.parse(url_for(path)).path

    request_path == target_path ? "active" : ""
  end

  # Format số tiền theo chuẩn Việt Nam
  def format_currency(amount)
    return "0 đ" if amount.nil? || amount.zero?

    # Chuyển về integer để loại bỏ phần thập phân .0
    amount_int = amount.to_i
    # Format với dấu chấm phân cách hàng nghìn theo chuẩn Việt Nam
    formatted_number = amount_int.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1.').reverse
    "#{formatted_number} đ"
  end

  # Format số tiền theo định dạng VNĐ
  def format_vnd_currency(amount)
    return "0 VNĐ" if amount.nil? || amount.zero?

    # Chuyển về integer để loại bỏ phần thập phân .0
    amount_int = amount.to_i
    # Format với dấu chấm phân cách hàng nghìn theo chuẩn Việt Nam
    formatted_number = amount_int.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1.').reverse
    "#{formatted_number} VNĐ"
  end
end
