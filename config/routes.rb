Rails.application.routes.draw do
  devise_for :users, controllers: {
    sessions: 'users/sessions',
  }

  get 'up' => 'rails/health#show', as: :rails_health_check

  root 'home#index'

  get '/gioi-thieu', to: 'about#index', as: 'about'
  get '/gioi-thieu-chung-chi', to: 'about#certificate_introduction', as: 'certificate_introduction'
  get '/gioi-thieu-noi-dung-bai-thi', to: 'about#exam_content_introduction', as: 'exam_content_introduction'
  get '/quy-dinh', to: 'regulations#index', as: 'regulations'
  get '/tra-cuu-ket-qua', to: 'search_results#index', as: 'search_results'
  get '/dang-ky-thi-lua-chon', to: 'exam_registration_forms#index', as: 'exam_registration_forms'
  post '/dang-ky-thi-lua-chon', to: 'exam_registration_forms#create'
  get '/lien-he', to: 'contacts#index', as: 'contact'
  resources :contacts, only: [:create]

  # SEO routes
  get '/sitemap.xml', to: 'sitemaps#index', defaults: { format: 'xml' }
  namespace :admin do
    # Master data management dashboard
    resources :master_data, only: [:index]

    # Unified exam management
    get 'exam_management', to: 'exam_management#index'
    post 'exam_management/create_center', to: 'exam_management#create_center'
    patch 'exam_management/:id/update_center', to: 'exam_management#update_center'
    delete 'exam_management/:id/destroy_center', to: 'exam_management#destroy_center'
    post 'exam_management/create_day', to: 'exam_management#create_day'
    delete 'exam_management/destroy_day/:id', to: 'exam_management#destroy_day'
    post 'exam_management/create_session', to: 'exam_management#create_session'
    delete 'exam_management/destroy_session/:id', to: 'exam_management#destroy_session'

    # Master data management routes
    resources :cities, only: [:index] do
      patch '/', to: 'cities#update', on: :collection
    end
    resources :colleges, only: [:index] do
      patch '/', to: 'colleges#update', on: :collection
    end
    resources :identity_document_types, only: [:index] do
      patch '/', to: 'identity_document_types#update', on: :collection
    end
    resources :support_choices, only: [:index] do
      patch '/', to: 'support_choices#update', on: :collection
    end
    resources :registration_reasons, only: [:index] do
      patch '/', to: 'registration_reasons#update', on: :collection
    end
    resources :certificate_delivery_methods, only: [:index] do
      patch '/', to: 'certificate_delivery_methods#update', on: :collection
    end
    resources :exam_centers, only: %i[index create update destroy] do
      collection do
        get :search
        patch :update_positions
      end
    end
    resources :exam_days, only: [:index] do
      collection do
        get :load_by_center
        patch '/', to: 'exam_days#update'
      end
    end
    resources :exam_sessions, only: [:index] do
      collection do
        get :load_by_day
        patch '/', to: 'exam_sessions#update'
      end
    end

    resources :exam_registration_forms, only: %i[index show] do
      collection do
        get :export_excel
      end
      member do
        patch :mark_as_paid
      end
    end
    resources :exam_results do
      collection do
        post :import
      end
    end
    resources :contacts, only: %i[index show destroy]
    resources :bank_transaction_imports, only: %i[index new create show destroy]
    root 'exam_registration_forms#index'
  end

  # Test routes for error pages (remove in production)
  if Rails.env.development?
    get '/test-404', to: proc { |_env| [404, {}, [Rails.public_path.join('404.html').read]] }
    get '/test-500', to: proc { |_env| [500, {}, [Rails.public_path.join('500.html').read]] }
    get '/test-422', to: proc { |_env| [422, {}, [Rails.public_path.join('422.html').read]] }
  end
end
