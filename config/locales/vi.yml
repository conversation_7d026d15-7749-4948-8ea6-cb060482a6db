vi:
  date:
    formats:
      default: "%d/%m/%Y"
    day_names:
      - <PERSON><PERSON> nhật
      - <PERSON><PERSON><PERSON> hai
      - <PERSON><PERSON><PERSON> ba
      - <PERSON><PERSON><PERSON> tư
      - Thứ năm
      - Thứ sáu
      - <PERSON>h<PERSON> bảy
    abbr_day_names:
      - CN
      - T2
      - T3
      - T4
      - T5
      - T6
      - T7
  activerecord:
    errors:
      messages:
        record_invalid: "Lỗi xác thực: %{errors}"
        restrict_dependent_destroy:
          has_one: "Không thể xóa bản ghi vì có %{record} phụ thuộc"
          has_many: "Không thể xóa bản ghi vì có %{record} phụ thuộc"
        blank: "không được để trống"
        invalid: "không hợp lệ"
        too_short: "quá ngắn (tối thiểu %{count} ký tự)"
    models:
      exam_result:
        one: "Kết quả thi"
        other: "Kết quả thi"
      contact:
        one: "<PERSON><PERSON><PERSON> hệ"
        other: "<PERSON><PERSON><PERSON> hệ"
    attributes:
      exam_result:
        student_name: "<PERSON><PERSON><PERSON> họ<PERSON> sinh"
        exam_date: "Ngày thi"
        score: "Điể<PERSON> số"
        subject: "<PERSON>ôn học"
        identification_number: "Số CMND/CCCD"
        dob: "<PERSON><PERSON><PERSON> sinh"
        student_code: "Mã học sinh"
      contact:
        name: "Tên"
        email: "Email"
        message: "Nội dung tin nhắn"
  contact:
    validations:
      name:
        blank: "không được để trống"
        too_short: "quá ngắn (tối thiểu %{count} ký tự)"
        too_long: "quá dài (tối đa %{count} ký tự)"
      email:
        blank: "không được để trống"
        invalid: "không đúng định dạng"
        too_long: "quá dài (tối đa %{count} ký tự)"
      message:
        blank: "không được để trống"
        too_short: "quá ngắn (tối thiểu %{count} ký tự)"
        too_long: "quá dài (tối đa %{count} ký tự)"
  errors:
    messages:
      too_short: "quá ngắn (tối thiểu %{count} ký tự)"
    attributes:
      message:
        too_short: "quá ngắn (tối thiểu %{count} ký tự)"
  exam_results:
    import:
      success: "Import dữ liệu thành công"
      error: "Lỗi import: %{error}"
      no_file: "Vui lòng chọn file để import"
  admin:
    contacts:
      destroy:
        success: "Đã xóa liên hệ thành công."
    common:
      success: "Thành công"
      error: "Có lỗi xảy ra!"
    certificate_delivery_methods:
      update:
        success: "Cập nhật phương thức nhận chứng chỉ thành công!"
        error: "Có lỗi xảy ra khi cập nhật phương thức nhận chứng chỉ: %{message}"
    cities:
      update:
        success: "Cập nhật thành phố thành công!"
        error: "Có lỗi xảy ra khi cập nhật thành phố: %{message}"
    colleges:
      update:
        success: "Cập nhật đại học/cao đẳng thành công!"
        error: "Có lỗi xảy ra khi cập nhật đại học/cao đẳng: %{message}"
    exam_centers:
      create:
        success: "Thêm trung tâm thi thành công!"
      update:
        success: "Cập nhật trung tâm thi thành công!"
      destroy:
        success: "Xóa trung tâm thi thành công!"
        error: "Không thể xóa trung tâm thi này!"
      update_positions:
        success: "Cập nhật thứ tự thành công!"
      update_visibility:
        success: "Cập nhật hiển thị trung tâm thi thành công! (%{visible_count}/%{total_count} trung tâm hiển thị)"
        error: "Có lỗi xảy ra khi cập nhật hiển thị trung tâm thi: %{message}"
    exam_days:
      update:
        success: "Cập nhật ngày thi thành công!"
        error: "Có lỗi xảy ra khi cập nhật ngày thi: %{message}"
    exam_sessions:
      update:
        success: "Cập nhật ca thi thành công!"
        error: "Có lỗi xảy ra khi cập nhật ca thi: %{message}"
    identity_document_types:
      update:
        success: "Cập nhật giấy tờ tùy thân thành công!"
        error: "Có lỗi xảy ra khi cập nhật giấy tờ tùy thân: %{message}"
    registration_reasons:
      update:
        success: "Cập nhật lý do đăng ký thi thành công!"
        error: "Có lỗi xảy ra khi cập nhật lý do đăng ký thi: %{message}"
    support_choices:
      update:
        success: "Cập nhật hỗ trợ đặc biệt thành công!"
        error: "Có lỗi xảy ra khi cập nhật hỗ trợ đặc biệt: %{message}"
    exam_registration_forms:
      mark_as_paid:
        success: "Đã đánh dấu thanh toán thành công!"
        error: "Có lỗi xảy ra khi cập nhật!"
        invalid: "Không thể đánh dấu thanh toán cho đơn này!"